plugins {
    id 'java'
    id "com.diffplug.spotless" version "7.0.0.BETA2"
    id 'maven-publish'
}

group = 'io.hydrax.aeron'
version = '0.3.28-SNAPSHOT'

repositories {
    mavenCentral()
}

spotless {
    java {
        targetExclude("build/**")
        googleJavaFormat('1.23.0').reflowLongStrings().formatJavadoc(false).reorderImports(false).groupArtifact('com.google.googlejavaformat:google-java-format')
    }
}

dependencies {

    testImplementation 'io.quarkus:quarkus-junit5:3.14.3'
    testImplementation 'org.mockito:mockito-core:5.12.0'
    testImplementation 'org.mockito:mockito-junit-jupiter:5.13.0'
    // Lombok
    compileOnly 'org.projectlombok:lombok:1.18.34'
    testCompileOnly 'org.projectlombok:lombok:1.18.34'
    annotationProcessor 'org.projectlombok:lombok:1.18.34'
    testAnnotationProcessor 'org.projectlombok:lombok:1.18.34'

    implementation 'io.aeron:aeron-all:1.43.0'
    implementation 'io.smallrye.config:smallrye-config-core:3.9.1'
    implementation 'io.netty:netty-common:4.1.115.Final'
    implementation 'org.slf4j:slf4j-api:2.0.16'
    implementation 'jakarta.enterprise:jakarta.enterprise.cdi-api:4.1.0'
    implementation group: 'com.lmax', name: 'disruptor', version: '4.0.0'


}

test {
    useJUnitPlatform()
}

tasks.build {
    dependsOn 'spotlessApply'
}

tasks.register('sourcesJar', Jar) {
    from sourceSets.main.allSource
    archiveClassifier.set("sources")
}


publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
            artifact sourcesJar
        }
    }

    repositories {
        maven {
            name = "GitHubPackages"
            url = uri("https://maven.pkg.github.com/HydraXTrader/exchange-generic-aeron-sdk")
            credentials {
                username = project.findProperty("gpr.user") ?: System.getenv("GIT_USERNAME")
                password = project.findProperty("gpr.token") ?: System.getenv("TOKEN")
            }
        }
    }
}